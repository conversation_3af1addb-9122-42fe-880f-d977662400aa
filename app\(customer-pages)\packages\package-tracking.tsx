import { Stack } from 'expo-router';

import { Container } from '~/components/Container';
import { CustomerScreenContent } from '~/components/customer-pages-components/CustomerScreenContent';

export default function PackageTracking() {
  return (
    <>
      <Container alignSelf='stretch' alignItems="stretch" justifyContent="flex-start" style={{ width: '100%', padding: 0, margin: 0 }} flex={1} mx="$0" px="$0" p="$0">
        <CustomerScreenContent path="app/(customer-pages)/packages/package-tracking.tsx" title="PackageTracking"></CustomerScreenContent>
      </Container>
    </>
  );
}