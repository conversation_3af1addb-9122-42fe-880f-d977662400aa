import { <PERSON><PERSON>View, Alert } from 'react-native';
import { Button, Input, Label, Text, View, YStack, Card, Separator, XStack, H3, H4 } from 'tamagui';
import { useRouter } from 'expo-router';
import { MotiView } from 'moti';
import { Ionicons } from '@expo/vector-icons';
import { useRequestPickupStore, useUpdateRequestPickup } from './useRequestPickupStore';
import { requestPickup } from '../../../services/apiService';
import { CustomTextField } from '../../CustomTextField';
import { useTranslation } from 'react-i18next';

export function RequestPickupForm() {
  const router = useRouter();
  const { t } = useTranslation();
  const {
    pickup,
    itemDescription,
    preferredTime,
    notes,
    updateField,
    reset
  } = useUpdateRequestPickup();
  const { addPickupRequest } = useRequestPickupStore();

  const isFormValid = () =>
    pickup && itemDescription.trim() && preferredTime.trim();

  return (
    <>
      {/* Enhanced Professional Header */}
      <View
        width="100%"
        style={{
          paddingTop: 50,
          paddingBottom: 30,
          paddingHorizontal: 0,
          borderBottomLeftRadius: 0,
          borderBottomRightRadius: 0,
          backgroundImage: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
          backgroundColor: '#f093fb',
        }}
      >
        <MotiView from={{ opacity: 0, translateY: -20 }} animate={{ opacity: 1, translateY: 0 }}>
          <YStack gap="$3" alignItems="center">
            <View
              style={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                borderRadius: 20,
                padding: 16,
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.3)',
              }}
            >
              <Ionicons name="hand-left" size={32} color="white" />
            </View>
            <Text fontSize="$10" fontWeight="800" color="white" textAlign="center">
              {t('requestPickup.title', { defaultValue: 'Request Pickup' })}
            </Text>
            <Text fontSize="$4" color="rgba(255,255,255,0.9)" textAlign="center" maxWidth={280}>
              {t('requestPickup.subtitle', { defaultValue: 'Schedule a convenient pickup service' })}
            </Text>
          </YStack>
        </MotiView>
      </View>

      <ScrollView
        contentContainerStyle={{ flexGrow: 1, paddingBottom: 40 }}
        style={{ backgroundColor: '#f8fafc', width: '100%' }}
      >
        <YStack gap="$5" px="$0" py="$4" width="100%">
          {/* Pickup Location */}
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ delay: 200, type: 'spring', damping: 15 }}
          >
            <Card
              elevate
              p="$6"
              br="$0"
              bg="white"
              borderWidth={0}
              borderColor="$gray4"
              shadowColor="$primary"
              shadowOffset={{ width: 0, height: 8 }}
              shadowOpacity={0.1}
              shadowRadius={16}
              width="100%"
              mx="$0"
            >
              <YStack gap="$5">
                <XStack ai="center" gap="$3">
                  <View
                    style={{
                      backgroundColor: '#f3f4f6',
                      borderRadius: 12,
                      padding: 8,
                    }}
                  >
                    <Ionicons name="location" size={20} color="#f093fb" />
                  </View>
                  <H4 color="$gray12" fontWeight="700">
                    {t('requestPickup.pickupLocation', { defaultValue: 'Pickup Location' })}
                  </H4>
                </XStack>

                <YStack gap="$3">
                  <Label fontSize="$4" fontWeight="600" color="$gray11">
                    {t('requestPickup.address', { defaultValue: 'Address' })} <Text color="$red9">*</Text>
                  </Label>
                  <XStack gap="$3" ai="center">
                    <XStack
                      flex={1}
                      alignItems="center"
                      gap="$3"
                      padding="$3"
                      borderWidth={2}
                      borderColor="$gray6"
                      borderRadius="$6"
                      backgroundColor="$background"
                      pressStyle={{ borderColor: '$primary' }}
                    >
                      <Ionicons name="location-outline" size={20} color="#f093fb" />
                      <Text
                        flex={1}
                        fontSize="$4"
                        color={pickup?.address ? '$gray12' : '$gray8'}
                      >
                        {pickup?.address || t('requestPickup.setOnMap', { defaultValue: 'Set location on map' })}
                      </Text>
                    </XStack>
                    <Button
                      size="$4"
                      bg="$primary"
                      color="white"
                      br="$6"
                      fontWeight="600"
                      pressStyle={{ bg: '$primaryPress' }}
                      onPress={() => router.push('/home/<USER>')}
                    >
                      {t('common.set', { defaultValue: 'Set' })}
                    </Button>
                  </XStack>
                </YStack>
              </YStack>
            </Card>
          </MotiView>

          {/* Item Details */}
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ delay: 400, type: 'spring', damping: 15 }}
          >
            <Card
              elevate
              p="$6"
              br="$0"
              bg="white"
              borderWidth={0}
              borderColor="$gray4"
              shadowColor="$primary"
              shadowOffset={{ width: 0, height: 8 }}
              shadowOpacity={0.1}
              shadowRadius={16}
              width="100%"
              mx="$0"
            >
              <YStack gap="$5">
                <XStack ai="center" gap="$3">
                  <View
                    style={{
                      backgroundColor: '#f3f4f6',
                      borderRadius: 12,
                      padding: 8,
                    }}
                  >
                    <Ionicons name="cube" size={20} color="#f093fb" />
                  </View>
                  <H4 color="$gray12" fontWeight="700">
                    {t('requestPickup.itemDetails', { defaultValue: 'Item Details' })}
                  </H4>
                </XStack>

                <YStack gap="$4">
                  <CustomTextField
                    icon="document-text-outline"
                    label={t('requestPickup.description', { defaultValue: 'Item Description' })}
                    placeholder={t('requestPickup.descriptionPlaceholder', { defaultValue: 'e.g. A small package of books' })}
                    value={itemDescription}
                    onChangeText={(text) => updateField('itemDescription', text)}
                    required
                    autoCapitalize="sentences"
                  />

                  <CustomTextField
                    icon="time-outline"
                    label={t('requestPickup.preferredTime', { defaultValue: 'Preferred Pickup Time' })}
                    placeholder={t('requestPickup.timePlaceholder', { defaultValue: 'e.g. Today at 4 PM' })}
                    value={preferredTime}
                    onChangeText={(text) => updateField('preferredTime', text)}
                    required
                  />

                  <YStack gap="$3">
                    <Label fontSize="$4" fontWeight="600" color="$gray11">
                      {t('requestPickup.notes', { defaultValue: 'Special Instructions' })}
                      <Text color="$gray8" fontSize="$3"> ({t('common.optional', { defaultValue: 'optional' })})</Text>
                    </Label>
                    <XStack
                      alignItems="flex-start"
                      gap="$3"
                      padding="$3"
                      borderWidth={2}
                      borderColor="$gray6"
                      borderRadius="$6"
                      backgroundColor="$background"
                      minHeight={80}
                      focusStyle={{ borderColor: '$primary' }}
                    >
                      <Ionicons name="chatbubble-outline" size={20} color="#f093fb" style={{ marginTop: 2 }} />
                      <Input
                        flex={1}
                        fontSize="$4"
                        placeholder={t('requestPickup.notesPlaceholder', { defaultValue: 'Optional instructions for the driver' })}
                        value={notes}
                        onChangeText={(text) => updateField('notes', text)}
                        multiline
                        numberOfLines={3}
                        borderWidth={0}
                        backgroundColor="transparent"
                        focusStyle={{
                          borderWidth: 0,
                          backgroundColor: "transparent"
                        }}
                      />
                    </XStack>
                  </YStack>
                </YStack>
              </YStack>
            </Card>
          </MotiView>

          {/* Enhanced Confirm Button */}
          <MotiView
            from={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 600, type: 'spring', damping: 15 }}
          >
            <Button
              width="100%"
              height={60}
              size="$6"
              bg={isFormValid() ? "$primary" : "$gray8"}
              color="white"
              br="$8"
              fontSize="$5"
              fontWeight="700"
              icon={<Ionicons name="hand-left" size={24} color="white" />}
              disabled={!isFormValid()}
              opacity={isFormValid() ? 1 : 0.6}
              pressStyle={{
                bg: isFormValid() ? "$primaryPress" : "$gray8",
                scale: 0.98
              }}
              style={{
                shadowColor: isFormValid() ? '#f093fb' : '#000',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: isFormValid() ? 0.3 : 0.1,
                shadowRadius: 8,
                elevation: 8,
              }}
            onPress={async () => {
              try {
                // Validate required fields
                if (!pickup?.address?.trim()) {
                  Alert.alert('Error', 'Please provide a pickup address');
                  return;
                }
                if (!itemDescription.trim()) {
                  Alert.alert('Error', 'Please provide item description');
                  return;
                }
                if (!preferredTime.trim()) {
                  Alert.alert('Error', 'Please provide preferred time');
                  return;
                }

                // Prepare pickup data for backend
                const pickupData = {
                  pickupAddress: {
                    street: pickup.address.trim(),
                    city: 'Nablus', // Default city
                    coordinates: {
                      lat: pickup.lat || 32.2211,
                      lng: pickup.lng || 35.2544
                    }
                  },
                  packageDetails: {
                    type: itemDescription.trim(),
                    size: 'medium' as const, // Default size
                    weight: '1' // Default weight as string (backend expects string)
                  },
                  preferredTime: preferredTime.trim(),
                  notes: notes?.trim() || ''
                };

                // Create pickup request via backend API
                const createdPickup = await requestPickup(pickupData);

                // Add to local store for immediate UI update
                addPickupRequest({
                  id: createdPickup.trackingNumber,
                  pickup,
                  itemDescription,
                  preferredTime,
                  notes,
                  driverName: 'Ali Alaa', // temporary
                  driverPhone: '0595959595',
                  status: 'pending',
                  createdAt: new Date().toISOString(),
                  estimatedTime: '45-60 mins',
                  cost: createdPickup.cost
                });

                reset();
                router.push('/home/<USER>');
              } catch (error) {
                console.error('Error creating pickup request:', error);

                // Extract error message properly
                let errorMessage = 'Failed to create pickup request. Please try again.';
                if (error && typeof error === 'object') {
                  if ('message' in error && typeof error.message === 'string') {
                    errorMessage = error.message;
                  } else if ('errors' in error && Array.isArray(error.errors) && error.errors.length > 0) {
                    errorMessage = error.errors[0].msg || errorMessage;
                  }
                }

                Alert.alert('Error', errorMessage);
              }
            }}
            >
              {t('requestPickup.confirmRequest', { defaultValue: 'Confirm Request' })}
            </Button>
          </MotiView>
        </YStack>
      </ScrollView>
    </>
  );
}
