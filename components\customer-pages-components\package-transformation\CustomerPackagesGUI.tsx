import { useState, useEffect, useCallback } from 'react';
import { ScrollView, RefreshControl, Alert, Dimensions } from 'react-native';
import { <PERSON>ton, Text, View, YStack, XStack, Card, Separator, Input, Sheet, H4, H5, H6, <PERSON><PERSON> } from 'tamagui';
import { <PERSON><PERSON>View } from 'moti';
import { Ionicons } from '@expo/vector-icons';
import { useRequestPickupStore } from './useRequestPickupStore';
import { useSendPackageStore } from './useSendPackageStore';
import { router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { getUserPackages } from '../../../services/apiService';

const { width } = Dimensions.get('window');

interface PackageData {
  packageId: string; // Backend uses packageId, not trackingNumber
  trackingNumber?: string; // Keep for backward compatibility
  status: string;
  createdAt: string;
  pickupAddress: {
    street: string;
    coordinates: { lat: number; lng: number };
  };
  deliveryAddress?: {
    street: string;
    coordinates: { lat: number; lng: number };
  };
  // Flat fields from backend
  recipientName: string;
  recipientPhone: string;
  senderName: string;
  senderPhone: string;
  packageDetails: {
    type?: string;
    description: string;
    weight?: number;
  };
  cost: number;
  priority: string;
  notes?: string;
  driverName?: string;
  driverPhone?: string;
  estimatedDeliveryTime?: string;
  actualDeliveryTime?: string;
}

interface PackageStats {
  total: number;
  totalSpent: number;
  byStatus: { [key: string]: number };
}

export function CustomerPackagesGUI() {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'sent' | 'pickup'>('sent');
  const { pickupRequests, addPickupRequest } = useRequestPickupStore();
  const { sendRequests, addSendRequest } = useSendPackageStore();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [packages, setPackages] = useState<PackageData[]>([]);
  const [filteredPackages, setFilteredPackages] = useState<PackageData[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [stats, setStats] = useState<PackageStats>({ total: 0, totalSpent: 0, byStatus: {} });
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // Enhanced fetch packages function with better error handling
  const fetchPackages = useCallback(async (pageNum: number = 1, isRefresh: boolean = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
        setError(null);
      } else if (pageNum === 1) {
        setLoading(true);
        setError(null);
      }

      const { packages: backendPackages, pagination } = await getUserPackages({
        page: pageNum,
        limit: 20
      });

      if (pageNum === 1 || isRefresh) {
        setPackages(backendPackages);
      } else {
        setPackages(prev => [...prev, ...backendPackages]);
      }

      // Calculate statistics
      const totalSpent = backendPackages.reduce((sum: number, pkg: PackageData) => sum + (pkg.cost || 0), 0);
      const statusCounts = backendPackages.reduce((acc: { [key: string]: number }, pkg: PackageData) => {
        acc[pkg.status] = (acc[pkg.status] || 0) + 1;
        return acc;
      }, {});

      setStats({
        total: backendPackages.length,
        totalSpent,
        byStatus: statusCounts
      });

      setHasMore(pagination?.page < pagination?.pages);

      // Sync with local stores for backward compatibility
      backendPackages.forEach((pkg: PackageData) => {
        const isPickupRequest = (pkg.packageId || pkg.trackingNumber)?.startsWith('PU-') ||
                               (pkg.deliveryAddress && pkg.pickupAddress &&
                                pkg.deliveryAddress.street === pkg.pickupAddress.street);

        if (!isPickupRequest) {
          const sendPackage = {
            id: pkg.packageId || pkg.trackingNumber,
            createdAt: pkg.createdAt,
            pickup: {
              address: pkg.pickupAddress.street,
              lat: pkg.pickupAddress.coordinates.lat,
              lng: pkg.pickupAddress.coordinates.lng
            },
            dropoff: {
              address: pkg.deliveryAddress.street,
              lat: pkg.deliveryAddress.coordinates.lat,
              lng: pkg.deliveryAddress.coordinates.lng
            },
            receiverName: pkg.recipientName || '',
            receiverPhone: pkg.recipientPhone || '',
            packageType: pkg.packageDetails.description || pkg.packageDetails.type || '',
            status: pkg.status,
            estimatedTime: pkg.estimatedDeliveryTime || '30-45 mins',
            driverName: pkg.driverName || 'Driver',
            driverPhone: pkg.driverPhone || '',
            cost: pkg.cost
          };

          if (!sendRequests.find(p => p.id === (pkg.packageId || pkg.trackingNumber))) {
            addSendRequest(sendPackage);
          }
        } else {
          const pickupRequest = {
            id: pkg.packageId || pkg.trackingNumber,
            createdAt: pkg.createdAt,
            pickup: {
              address: pkg.pickupAddress.street,
              lat: pkg.pickupAddress.coordinates.lat,
              lng: pkg.pickupAddress.coordinates.lng
            },
            packageType: pkg.packageDetails.type || pkg.packageDetails.description,
            status: pkg.status,
            estimatedTime: '45-60 mins',
            driverName: pkg.driverName || 'Driver',
            driverPhone: pkg.driverPhone || '',
            cost: pkg.cost,
            notes: pkg.notes || ''
          };

          if (!pickupRequests.find(p => p.id === (pkg.packageId || pkg.trackingNumber))) {
            addPickupRequest(pickupRequest);
          }
        }
      });

    } catch (error) {
      console.error('Error fetching packages:', error);
      setError(t('packages.failedToLoad', { defaultValue: 'Failed to load packages. Please try again.' }));

      if (pageNum === 1) {
        Alert.alert(
          t('common.error', { defaultValue: 'Error' }),
          t('packages.failedToLoad', { defaultValue: 'Failed to load packages. Please try again.' }),
          [{ text: t('common.ok', { defaultValue: 'OK' }) }]
        );
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [t, addSendRequest, addPickupRequest, sendRequests, pickupRequests]);

  // Initial load
  useEffect(() => {
    fetchPackages(1);
  }, [fetchPackages]);

  // Filter and search functionality
  useEffect(() => {
    let filtered = packages.filter(pkg => {
      const isPickupRequest = (pkg.packageId || pkg.trackingNumber)?.startsWith('PU-') ||
                             (pkg.deliveryAddress && pkg.pickupAddress &&
                              pkg.deliveryAddress.street === pkg.pickupAddress.street);
      const isSentPackage = !isPickupRequest;
      const isCorrectTab = (activeTab === 'sent' && isSentPackage) || (activeTab === 'pickup' && isPickupRequest);

      if (!isCorrectTab) return false;

      // Status filter
      if (selectedStatus !== 'all' && pkg.status !== selectedStatus) return false;

      // Search filter
      if (searchQuery.trim()) {
        const query = searchQuery.toLowerCase();
        const searchableText = [
          pkg.packageId || pkg.trackingNumber,
          pkg.pickupAddress.street,
          pkg.deliveryAddress?.street,
          pkg.recipientInfo?.name,
          pkg.packageDetails.description,
          pkg.packageDetails.type,
          pkg.status
        ].filter(Boolean).join(' ').toLowerCase();

        if (!searchableText.includes(query)) return false;
      }

      return true;
    });

    setFilteredPackages(filtered);
  }, [packages, activeTab, selectedStatus, searchQuery]);

  // Pull to refresh handler
  const onRefresh = useCallback(() => {
    setPage(1);
    fetchPackages(1, true);
  }, [fetchPackages]);

  // Load more handler
  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchPackages(nextPage);
    }
  }, [loading, hasMore, page, fetchPackages]);

  // Status options for filtering
  const statusOptions = [
    { value: 'all', label: t('common.all', { defaultValue: 'All' }) },
    { value: 'pending', label: t('packages.statuses.pending', { defaultValue: 'Pending' }) },
    { value: 'confirmed', label: t('packages.statuses.confirmed', { defaultValue: 'Confirmed' }) },
    { value: 'picked_up', label: t('packages.statuses.picked_up', { defaultValue: 'Picked Up' }) },
    { value: 'in_transit', label: t('packages.statuses.in_transit', { defaultValue: 'In Transit' }) },
    { value: 'out_for_delivery', label: t('packages.statuses.out_for_delivery', { defaultValue: 'Out for Delivery' }) },
    { value: 'delivered', label: t('packages.statuses.delivered', { defaultValue: 'Delivered' }) },
    { value: 'cancelled', label: t('packages.statuses.cancelled', { defaultValue: 'Cancelled' }) }
  ];

  const data = filteredPackages;

  return (
    <>
      {/* Enhanced Gradient Header with Stats */}
      <View
        width="100%"
        style={{
          paddingTop: 50,
          paddingBottom: 30,
          paddingHorizontal: 24,
          borderBottomLeftRadius: 32,
          borderBottomRightRadius: 32,
          backgroundImage: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          backgroundColor: '#667eea',
        }}
      >
        <MotiView from={{ opacity: 0, translateY: -20 }} animate={{ opacity: 1, translateY: 0 }}>
          <Text fontSize="$10" fontWeight="800" color="white" textAlign="center" mb="$4">
            📦 {t('packages.myPackages', { defaultValue: 'My Packages' })}
          </Text>

          {/* Statistics Cards */}
          <XStack jc="space-between" gap="$3" mt="$3">
            <MotiView
              from={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 200 }}
              style={{ flex: 1 }}
            >
              <Card bg="rgba(255,255,255,0.15)" p="$3" br="$6" bc="rgba(255,255,255,0.2)" bw={1}>
                <Text color="white" fontSize="$2" opacity={0.9} textAlign="center">
                  {t('orders.stats.total', { defaultValue: 'Total Orders' })}
                </Text>
                <Text color="white" fontSize="$7" fontWeight="bold" textAlign="center">
                  {stats.total}
                </Text>
              </Card>
            </MotiView>

            <MotiView
              from={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 400 }}
              style={{ flex: 1 }}
            >
              <Card bg="rgba(255,255,255,0.15)" p="$3" br="$6" bc="rgba(255,255,255,0.2)" bw={1}>
                <Text color="white" fontSize="$2" opacity={0.9} textAlign="center">
                  {t('orders.stats.totalSpent', { defaultValue: 'Total Spent' })}
                </Text>
                <Text color="white" fontSize="$7" fontWeight="bold" textAlign="center">
                  ${stats.totalSpent.toFixed(2)}
                </Text>
              </Card>
            </MotiView>
          </XStack>
        </MotiView>
      </View>

      <ScrollView
        contentContainerStyle={{ flexGrow: 1, paddingBottom: 120 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        onScroll={({ nativeEvent }) => {
          const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
          const isCloseToBottom = layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;
          if (isCloseToBottom && !loading && hasMore) {
            loadMore();
          }
        }}
        scrollEventThrottle={400}
      >
        {/* Search and Filter Section */}
        <YStack px="$4" pt="$4" gap="$3">
          <XStack gap="$3" ai="center">
            <View flex={1}>
              <Input
                placeholder={t('packages.searchPlaceholder', { defaultValue: 'Search packages...' })}
                value={searchQuery}
                onChangeText={setSearchQuery}
                size="$4"
                br="$6"
                bg="white"
                bw={1}
                bc="$gray6"
                style={{
                  shadowColor: '#000',
                  shadowOpacity: 0.1,
                  shadowRadius: 4,
                  elevation: 2,
                }}
              />
            </View>
            <Button
              size="$4"
              br="$6"
              bg="$primary"
              onPress={() => setShowFilters(true)}
              icon={<Ionicons name="filter" size={18} color="white" />}
              style={{
                shadowColor: '#000',
                shadowOpacity: 0.2,
                shadowRadius: 4,
                elevation: 3,
              }}
            >
              {t('orders.filterByStatus', { defaultValue: 'Filter' })}
            </Button>
          </XStack>
        </YStack>

        {/* Enhanced Tabs */}
        <XStack jc="center" mt="$4" mb="$3" gap="$3" px="$4">
          <MotiView
            from={{ opacity: 0, translateX: -20 }}
            animate={{ opacity: 1, translateX: 0 }}
            style={{ flex: 1 }}
          >
            <Button
              size="$4"
              chromeless
              px="$4"
              py="$3"
              br="$8"
              bg={activeTab === 'sent' ? '$primary' : 'white'}
              color={activeTab === 'sent' ? 'white' : '$gray10'}
              onPress={() => setActiveTab('sent')}
              style={{
                shadowColor: activeTab === 'sent' ? '#667eea' : '#000',
                shadowOpacity: activeTab === 'sent' ? 0.3 : 0.1,
                shadowRadius: 8,
                elevation: activeTab === 'sent' ? 5 : 2,
                borderWidth: activeTab === 'sent' ? 0 : 1,
                borderColor: '#e0e0e0',
              }}
            >
              <XStack ai="center" gap="$2">
                <Ionicons name="send" size={16} color={activeTab === 'sent' ? 'white' : '#666'} />
                <Text color={activeTab === 'sent' ? 'white' : '$gray10'} fontWeight="600">
                  {t('packages.sentPackages', { defaultValue: 'Sent Packages' })}
                </Text>
              </XStack>
            </Button>
          </MotiView>

          <MotiView
            from={{ opacity: 0, translateX: 20 }}
            animate={{ opacity: 1, translateX: 0 }}
            style={{ flex: 1 }}
          >
            <Button
              size="$4"
              chromeless
              px="$4"
              py="$3"
              br="$8"
              bg={activeTab === 'pickup' ? '$primary' : 'white'}
              color={activeTab === 'pickup' ? 'white' : '$gray10'}
              onPress={() => setActiveTab('pickup')}
              style={{
                shadowColor: activeTab === 'pickup' ? '#667eea' : '#000',
                shadowOpacity: activeTab === 'pickup' ? 0.3 : 0.1,
                shadowRadius: 8,
                elevation: activeTab === 'pickup' ? 5 : 2,
                borderWidth: activeTab === 'pickup' ? 0 : 1,
                borderColor: '#e0e0e0',
              }}
            >
              <XStack ai="center" gap="$2">
                <Ionicons name="hand-left" size={16} color={activeTab === 'pickup' ? 'white' : '#666'} />
                <Text color={activeTab === 'pickup' ? 'white' : '$gray10'} fontWeight="600">
                  {t('packages.pickupRequests', { defaultValue: 'Pickup Requests' })}
                </Text>
              </XStack>
            </Button>
          </MotiView>
        </XStack>

        {/* Enhanced Package Cards */}
        <YStack px="$4" pt="$2" gap="$4" width="100%" alignSelf='center'>
          {loading && page === 1 ? (
            <MotiView from={{ opacity: 0 }} animate={{ opacity: 1 }}>
              <YStack ai="center" jc="center" py="$8">
                <Spinner size="large" color="$primary" />
                <Text mt="$3" color="$gray9" fontSize="$5">
                  {t('common.loading', { defaultValue: 'Loading...' })}
                </Text>
              </YStack>
            </MotiView>
          ) : error ? (
            <MotiView from={{ opacity: 0 }} animate={{ opacity: 1 }}>
              <Card bg="$red1" p="$4" br="$6" bc="$red6" bw={1} mx="$2">
                <YStack ai="center" gap="$3">
                  <Ionicons name="alert-circle" size={48} color="#ef4444" />
                  <Text textAlign="center" color="$red10" fontSize="$5" fontWeight="600">
                    {error}
                  </Text>
                  <Button
                    size="$3"
                    bg="$red9"
                    color="white"
                    br="$6"
                    onPress={() => fetchPackages(1, true)}
                  >
                    {t('common.retry', { defaultValue: 'Retry' })}
                  </Button>
                </YStack>
              </Card>
            </MotiView>
          ) : data.length === 0 ? (
            <MotiView from={{ opacity: 0 }} animate={{ opacity: 1 }}>
              <Card bg="$gray1" p="$6" br="$8" mx="$2">
                <YStack ai="center" gap="$4">
                  <Ionicons name="cube-outline" size={64} color="#9ca3af" />
                  <Text textAlign="center" color="$gray9" fontSize="$6" fontWeight="600">
                    {t('packages.noPackagesYet', {
                      type: activeTab === 'sent'
                        ? t('packages.sentPackages', { defaultValue: 'sent packages' }).toLowerCase()
                        : t('packages.pickupRequests', { defaultValue: 'pickup requests' }).toLowerCase(),
                      defaultValue: `No ${activeTab === 'sent' ? 'sent packages' : 'pickup requests'} yet.`
                    })}
                  </Text>
                  <Text textAlign="center" color="$gray8" fontSize="$4">
                    {activeTab === 'sent'
                      ? t('packages.sendFirstPackage', { defaultValue: 'Send your first package' })
                      : t('packages.requestFirstPickup', { defaultValue: 'Request your first pickup' })
                    }
                  </Text>
                </YStack>
              </Card>
            </MotiView>
          ) : (
            data.map((item, index) => {
              const isDelivered = item.status === 'delivered';
              const isPending = item.status === 'pending';
              const isInTransit = ['picked_up', 'in_transit', 'out_for_delivery'].includes(item.status);

              return (
                <MotiView
                    key={item.packageId || item.trackingNumber || index}
                    from={{ opacity: 0, translateY: 20, scale: 0.95 }}
                    animate={{ opacity: 1, translateY: 0, scale: 1 }}
                    transition={{ delay: index * 100, type: 'spring', damping: 15 }}
                >
                    <Card
                    p="$0"
                    br="$8"
                    bg="white"
                    mx="$2"
                    style={{
                        overflow: 'hidden',
                        shadowColor: '#000',
                        shadowOpacity: 0.12,
                        shadowRadius: 12,
                        shadowOffset: { width: 0, height: 4 },
                        elevation: 8,
                        borderWidth: 1,
                        borderColor: isDelivered ? '#10b981' : isInTransit ? '#f59e0b' : isPending ? '#6b7280' : '#e5e7eb',
                    }}
                    >
                    {/* Enhanced Card Header */}
                    <View
                        px="$4"
                        py="$4"
                        jc="space-between"
                        ai="center"
                        flexDirection="row"
                        style={{
                          background: isDelivered
                            ? 'linear-gradient(135deg, #10b981, #059669)'
                            : isInTransit
                            ? 'linear-gradient(135deg, #f59e0b, #d97706)'
                            : isPending
                            ? 'linear-gradient(135deg, #6b7280, #4b5563)'
                            : 'linear-gradient(135deg, #667eea, #764ba2)',
                          backgroundColor: isDelivered ? '#10b981' : isInTransit ? '#f59e0b' : isPending ? '#6b7280' : '#667eea',
                        }}
                    >
                        <YStack gap="$1">
                          <Text color="white" fontWeight="800" fontSize="$5">
                            {activeTab === 'sent' ? '📦 Package Delivery' : '📥 Pickup Request'}
                          </Text>
                          <Text color="white" opacity={0.9} fontSize="$3">
                            #{item.packageId || item.trackingNumber || `PKG-${index + 1}`}
                          </Text>
                        </YStack>
                        <View
                          bg="rgba(255,255,255,0.2)"
                          p="$2"
                          br="$6"
                        >
                          <Ionicons
                            name={isDelivered ? "checkmark-circle" : isInTransit ? "car" : "time"}
                            size={24}
                            color="white"
                          />
                        </View>
                    </View>

                    {/* Enhanced Card Content */}
                    <YStack p="$4" bg="white" gap="$4">
                        {/* Status Badge */}
                        <XStack jc="space-between" ai="center">
                          <View
                            bg={isDelivered ? '$green2' : isInTransit ? '$orange2' : isPending ? '$gray4' : '$blue2'}
                            px="$3"
                            py="$2"
                            br="$6"
                          >
                            <Text
                              color={isDelivered ? '$green10' : isInTransit ? '$orange10' : isPending ? '$gray10' : '$blue10'}
                              fontSize="$3"
                              fontWeight="600"
                            >
                              {t(`packages.statuses.${item.status?.toLowerCase().replace(' ', '_')}`, { defaultValue: item.status })}
                            </Text>
                          </View>

                          {item.cost && (
                            <Text fontSize="$6" fontWeight="bold" color="$primary">
                              ${item.cost.toFixed(2)}
                            </Text>
                          )}
                        </XStack>

                        {/* Package Information */}
                        <YStack gap="$3">
                          {activeTab === 'sent' ? (
                              <>
                              <EnhancedInfoRow
                                label={t('packages.from', { defaultValue: 'From' })}
                                icon="location"
                                value={item.pickupAddress?.street || item.pickup?.address}
                                iconColor="#10b981"
                              />
                              {item.deliveryAddress && (
                                <EnhancedInfoRow
                                  label={t('packages.to', { defaultValue: 'To' })}
                                  icon="navigate"
                                  value={item.deliveryAddress.street || item.dropoff?.address}
                                  iconColor="#3b82f6"
                                />
                              )}
                              {item.recipientInfo && (
                                <EnhancedInfoRow
                                  label={t('packages.recipient', { defaultValue: 'Recipient' })}
                                  icon="person"
                                  value={`${item.recipientInfo.name} (${item.recipientInfo.phone})`}
                                  iconColor="#8b5cf6"
                                />
                              )}
                              <EnhancedInfoRow
                                label={t('packages.packageType', { defaultValue: 'Type' })}
                                icon="cube"
                                value={item.packageDetails?.type || item.packageDetails?.description || item.packageType}
                                iconColor="#f59e0b"
                              />
                              </>
                          ) : (
                              <>
                              <EnhancedInfoRow
                                label={t('packages.pickupAddress', { defaultValue: 'Pickup Location' })}
                                icon="location"
                                value={item.pickupAddress?.street || item.pickup?.address}
                                iconColor="#10b981"
                              />
                              <EnhancedInfoRow
                                label={t('packages.item', { defaultValue: 'Item' })}
                                icon="cube"
                                value={item.packageDetails?.description || item.packageDetails?.type || item.packageType}
                                iconColor="#f59e0b"
                              />
                              {item.notes && (
                                <EnhancedInfoRow
                                  label={t('packages.notes', { defaultValue: 'Notes' })}
                                  icon="document-text"
                                  value={item.notes}
                                  iconColor="#6b7280"
                                />
                              )}
                              </>
                          )}

                          {item.driverName && (
                              <EnhancedInfoRow
                                label={t('packages.driver', { defaultValue: 'Driver' })}
                                icon="person-circle"
                                value={`${item.driverName}${item.driverPhone ? ` (${item.driverPhone})` : ''}`}
                                iconColor="#ef4444"
                              />
                          )}
                        </YStack>

                        {/* Action Buttons */}
                        <XStack gap="$3" mt="$2">
                          <Button
                            flex={1}
                            size="$4"
                            br="$8"
                            bg="$primary"
                            color="white"
                            fontWeight="600"
                            onPress={() => {
                              const trackingId = item.packageId || item.trackingNumber;
                              if (trackingId) {
                                router.push({
                                  pathname: "/packages/package-tracking",
                                  params: {
                                    id: trackingId,
                                    type: activeTab
                                  }
                                });
                              } else {
                                Alert.alert(
                                  t('packages.error', { defaultValue: 'Error' }),
                                  t('packages.noTrackingNumber', { defaultValue: 'No tracking number available for this package' })
                                );
                              }
                            }}
                            style={{
                              shadowColor: '#667eea',
                              shadowOpacity: 0.3,
                              shadowRadius: 8,
                              elevation: 4,
                            }}
                          >
                            <XStack ai="center" gap="$2">
                              <Ionicons name="eye" size={18} color="white" />
                              <Text color="white" fontWeight="600">
                                {t('packages.trackPackage', { defaultValue: 'Track Package' })}
                              </Text>
                            </XStack>
                          </Button>
                        </XStack>
                    </YStack>

                    </Card>
                </MotiView>
              )
            })
          )}

          {/* Load More Indicator */}
          {loading && page > 1 && (
            <MotiView from={{ opacity: 0 }} animate={{ opacity: 1 }}>
              <YStack ai="center" py="$4">
                <Spinner size="small" color="$primary" />
                <Text mt="$2" color="$gray9" fontSize="$3">
                  {t('common.loading', { defaultValue: 'Loading more...' })}
                </Text>
              </YStack>
            </MotiView>
          )}

          <Separator my="$4" />
        </YStack>
      </ScrollView>

      {/* Filter Sheet */}
      <Sheet
        modal
        open={showFilters}
        onOpenChange={setShowFilters}
        snapPoints={[60]}
        dismissOnSnapToBottom
      >
        <Sheet.Overlay />
        <Sheet.Handle />
        <Sheet.Frame p="$4" bg="white">
          <YStack gap="$4">
            <H4 textAlign="center">{t('orders.filterByStatus', { defaultValue: 'Filter by Status' })}</H4>

            <YStack gap="$3">
              {statusOptions.map((option) => (
                <Button
                  key={option.value}
                  size="$4"
                  br="$6"
                  bg={selectedStatus === option.value ? '$primary' : '$gray2'}
                  color={selectedStatus === option.value ? 'white' : '$gray10'}
                  onPress={() => {
                    setSelectedStatus(option.value);
                    setShowFilters(false);
                  }}
                >
                  {option.label}
                </Button>
              ))}
            </YStack>

            <Button
              size="$4"
              br="$6"
              bg="$gray6"
              color="$gray11"
              onPress={() => setShowFilters(false)}
              mt="$2"
            >
              {t('common.close', { defaultValue: 'Close' })}
            </Button>
          </YStack>
        </Sheet.Frame>
      </Sheet>
    </>
  );
}

function EnhancedInfoRow({
  label,
  icon,
  value,
  iconColor = "#888",
}: {
  label: string;
  icon: keyof typeof Ionicons.glyphMap;
  value?: string;
  iconColor?: string;
}) {
  if (!value) return null;

  return (
    <XStack ai="center" gap="$3" p="$2" bg="$gray1" br="$4">
      <View
        bg="white"
        p="$2"
        br="$4"
        style={{
          shadowColor: iconColor,
          shadowOpacity: 0.2,
          shadowRadius: 4,
          elevation: 2,
        }}
      >
        <Ionicons name={icon} size={18} color={iconColor} />
      </View>
      <YStack flex={1}>
        <Text color="$gray8" fontSize="$2" fontWeight="500" mb="$1">
          {label}
        </Text>
        <Text color="$gray11" fontSize="$4" fontWeight="600" numberOfLines={2}>
          {value}
        </Text>
      </YStack>
    </XStack>
  );
}

// Legacy InfoRow for backward compatibility
function InfoRow({
  label,
  icon,
  value,
}: {
  label: string;
  icon: keyof typeof Ionicons.glyphMap;
  value?: string;
}) {
  if (!value) return null;

  return (
    <XStack ai="center" gap="$3">
      <Ionicons name={icon} size={18} color="#888" />
      <Text color="$gray9" fontSize="$4">
        <Text color="$gray11" fontWeight="600">{label}:</Text> {value}
      </Text>
    </XStack>
  );
}
