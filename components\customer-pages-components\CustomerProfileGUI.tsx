import React, { useState, useEffect } from 'react';
import { Card, YStack, Text, View, H2, H3, H4, XStack, Paragraph, Button, Input, Label, Sheet, Separator, Switch } from 'tamagui';
import { Avatar, AvatarImage, AvatarFallback } from '@tamagui/avatar'
import { Ionicons } from '@expo/vector-icons'
import { ImageBackground, Pressable, View as RNView, ScrollView, Alert, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useCurrentUserData } from '../useCurrentUserData';
import { apiService } from '../../services/apiService';
import { MotiView } from 'moti';

// Define types locally since they're not exported from apiService
interface UpdateProfileRequest {
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  address?: string;
  city?: string;
  country?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  notifications?: boolean;
}

interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}
import { LanguageSwitcher } from '../common/LanguageSwitcher';
import { useLanguageStore } from '../../stores/languageStore';

type CustomerProfileGUIProps = {
  title: string;
};

export const CustomerProfileGUI = ({ title }: CustomerProfileGUIProps) => {
    const { t } = useTranslation();
    const { currentLanguage, isRTL } = useLanguageStore();
    const { user, isLoading, loadUserProfile, initializeUser } = useCurrentUserData();
    const [editMode, setEditMode] = useState<'profile' | 'password' | null>(null);
    const [updating, setUpdating] = useState(false);

    // Profile edit form state
    const [profileForm, setProfileForm] = useState<UpdateProfileRequest>({});

    // Password change form state
    const [passwordForm, setPasswordForm] = useState<ChangePasswordRequest>({
        currentPassword: '',
        newPassword: ''
    });



    // Load user profile on component mount
    useEffect(() => {
        const initializeUserData = async () => {
            console.log('🔄 Profile page: Initializing user...');
            if (!user) {
                console.log('🔄 Profile page: User not found, initializing...');
                try {
                    await initializeUser();
                } catch (error) {
                    console.log('❌ Profile page: Failed to initialize user:', error);
                }
            } else {
                console.log('✅ Profile page: User already loaded:', user.email);
            }
        };
        initializeUserData();
    }, [user, initializeUser]);

    // Initialize form with user data when user loads
    useEffect(() => {
        if (user) {
            setProfileForm({
                firstName: user.firstName || '',
                lastName: user.lastName || '',
                phoneNumber: user.phoneNumber || '',
                address: user.address || '',
                city: user.city || '',
                country: user.country || '',
                dateOfBirth: user.dateOfBirth || '',
                gender: user.gender || undefined
            });
        }
    }, [user]);

    // Reset form to original user data
    const resetProfileForm = () => {
        if (user) {
            setProfileForm({
                firstName: user.firstName || '',
                lastName: user.lastName || '',
                phoneNumber: user.phoneNumber || '',
                address: user.address || '',
                city: user.city || '',
                country: user.country || '',
                dateOfBirth: user.dateOfBirth || '',
                gender: user.gender || undefined
            });
        }
    };

    const handleUpdateProfile = async () => {
        if (!user) return;

        // Basic validation
        if (!profileForm.firstName?.trim() || !profileForm.lastName?.trim()) {
            Alert.alert('Error', 'First name and last name are required');
            return;
        }

        if (!profileForm.phoneNumber?.trim()) {
            Alert.alert('Error', 'Phone number is required');
            return;
        }

        setUpdating(true);
        try {
            console.log('📤 Sending profile update:', profileForm);
            const response = await apiService.updateProfile(profileForm);
            console.log('📥 Profile update response:', response);

            if (response.success) {
                Alert.alert('Success', 'Profile updated successfully');
                await loadUserProfile(); // Reload user data
                setEditMode(null);
                // Reset form to updated values
                setProfileForm({
                    firstName: response.data?.user?.firstName || profileForm.firstName,
                    lastName: response.data?.user?.lastName || profileForm.lastName,
                    phoneNumber: response.data?.user?.phoneNumber || profileForm.phoneNumber,
                    address: response.data?.user?.address || profileForm.address,
                    city: response.data?.user?.city || profileForm.city,
                    country: response.data?.user?.country || profileForm.country,
                    dateOfBirth: response.data?.user?.dateOfBirth || profileForm.dateOfBirth,
                    gender: response.data?.user?.gender || profileForm.gender
                });
            } else {
                Alert.alert('Error', response.message || 'Failed to update profile');
            }
        } catch (error) {
            Alert.alert('Error', 'Failed to update profile. Please check your connection and try again.');
            console.error('Profile update error:', error);
        } finally {
            setUpdating(false);
        }
    };

    const handleChangePassword = async () => {
        if (!passwordForm.currentPassword || !passwordForm.newPassword) {
            Alert.alert('Error', 'Please fill in all password fields');
            return;
        }

        if (passwordForm.newPassword.length < 6) {
            Alert.alert('Error', 'New password must be at least 6 characters long');
            return;
        }

        setUpdating(true);
        try {
            console.log('📤 Sending password change request');
            const response = await apiService.changePassword({
                currentPassword: passwordForm.currentPassword,
                newPassword: passwordForm.newPassword
            });
            console.log('📥 Password change response:', response);

            if (response.success) {
                Alert.alert('Success', 'Password changed successfully');
                setPasswordForm({ currentPassword: '', newPassword: '' });
                setEditMode(null);
            } else {
                Alert.alert('Error', response.message || 'Failed to change password');
            }
        } catch (error) {
            Alert.alert('Error', 'Failed to change password');
            console.error('Password change error:', error);
        } finally {
            setUpdating(false);
        }
    };

    const handleLogout = async () => {
        Alert.alert(
            'Logout',
            'Are you sure you want to logout?',
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Logout',
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            await apiService.logout();
                            router.replace('/');
                        } catch (error) {
                            console.error('Logout error:', error);
                            router.replace('/');
                        }
                    }
                }
            ]
        );
    };

    if (isLoading) {
        return (
            <YStack flex={1} alignItems="center" justifyContent="center" backgroundColor="#fff">
                <ActivityIndicator size="large" color="#67B329" />
                <Text marginTop="$4" color="$gray9">{t('common.loading', { defaultValue: 'Loading...' })}</Text>
            </YStack>
        );
    }

    if (!user) {
        return (
            <YStack flex={1} alignItems="center" justifyContent="center" backgroundColor="#fff">
                <Ionicons name="person-circle-outline" size={64} color="#ccc" />
                <Text marginTop="$4" color="$gray9">{t('profile.failedToLoad', { defaultValue: 'Failed to load profile' })}</Text>
                <Button
                    marginTop="$4"
                    onPress={loadUserProfile}
                    backgroundColor="#67B329"
                >
                    {t('common.retry', { defaultValue: 'Retry' })}
                </Button>
            </YStack>
        );
    }

    return (
        <YStack flex={1} backgroundColor="#f8fafc" width="100%">
            {/* Enhanced Professional Header */}
            <View
                width="100%"
                style={{
                    paddingTop: 50,
                    paddingBottom: 40,
                    paddingHorizontal: 0,
                    borderBottomLeftRadius: 0,
                    borderBottomRightRadius: 0,
                    backgroundImage: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    backgroundColor: '#667eea',
                }}
            >
                <MotiView from={{ opacity: 0, translateY: -20 }} animate={{ opacity: 1, translateY: 0 }}>
                    <YStack gap="$4" alignItems="center">
                        {/* Avatar Section */}
                        <MotiView
                            from={{ scale: 0.8, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{ delay: 200, type: 'spring', damping: 15 }}
                        >
                            <View
                                style={{
                                    backgroundColor: 'rgba(255,255,255,0.2)',
                                    borderRadius: 60,
                                    padding: 4,
                                    borderWidth: 3,
                                    borderColor: 'rgba(255,255,255,0.3)',
                                }}
                            >
                                <Avatar circular size={100}>
                                    <AvatarImage
                                        src={`https://ui-avatars.com/api/?name=${user.firstName}+${user.lastName}&background=667eea&color=fff&size=200`}
                                    />
                                    <AvatarFallback backgroundColor="rgba(255,255,255,0.3)" alignItems="center" justifyContent="center">
                                        <Ionicons name="person" size={40} color="white" />
                                    </AvatarFallback>
                                </Avatar>
                            </View>
                        </MotiView>

                        {/* User Info */}
                        <YStack alignItems="center" gap="$2">
                            <H2 fontSize="$9" fontWeight="800" color="white" textAlign="center">
                                {user.firstName} {user.lastName}
                            </H2>
                            <Text fontSize="$4" color="rgba(255,255,255,0.9)" textAlign="center">
                                @{user.username}
                            </Text>
                            <Text fontSize="$3" color="rgba(255,255,255,0.8)" textAlign="center">
                                {user.email}
                            </Text>
                            <Text fontSize="$4" fontWeight="600" color="rgba(255,255,255,0.9)" textAlign="center">
                                {user.phoneNumber}
                            </Text>
                        </YStack>
                    </YStack>
                </MotiView>
            </View>

            {/* Professional Profile Content */}
            <ScrollView
                contentContainerStyle={{ flexGrow: 1, paddingBottom: 40 }}
                style={{ backgroundColor: '#f8fafc', width: '100%' }}
            >
                <YStack gap="$4" px="$0" py="$4" width="100%">
                    {/* Personal Information Card */}
                    <MotiView
                        from={{ opacity: 0, translateY: 20 }}
                        animate={{ opacity: 1, translateY: 0 }}
                        transition={{ delay: 300, type: 'spring', damping: 15 }}
                    >
                        <Card
                            br="$0"
                            borderWidth={0}
                            width="100%"
                            mx="$0"
                            backgroundColor="white"
                            shadowColor="#000"
                            shadowOffset={{ width: 0, height: 4 }}
                            shadowOpacity={0.1}
                            shadowRadius={8}
                            elevation={5}
                        >
                            <YStack p="$5" gap="$4">
                                {/* Header */}
                                <XStack alignItems="center" justifyContent="space-between">
                                    <XStack alignItems="center" gap="$3">
                                        <View
                                            style={{
                                                backgroundColor: '#667eea',
                                                borderRadius: 12,
                                                width: 48,
                                                height: 48,
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                            }}
                                        >
                                            <Ionicons name="person" size={24} color="white" />
                                        </View>
                                        <YStack>
                                            <H4 fontSize="$6" fontWeight="700" color="$gray12">
                                                {t('profile.personalInfo', { defaultValue: 'Personal Information' })}
                                            </H4>
                                            <Text fontSize="$3" color="$gray9">
                                                {t('profile.managePersonalDetails', { defaultValue: 'Manage your personal details' })}
                                            </Text>
                                        </YStack>
                                    </XStack>
                                    <Pressable onPress={() => setEditMode('profile')}>
                                        <View
                                            style={{
                                                backgroundColor: '#f1f5f9',
                                                borderRadius: 8,
                                                padding: 8,
                                            }}
                                        >
                                            <Ionicons name="pencil" size={20} color="#667eea" />
                                        </View>
                                    </Pressable>
                                </XStack>

                                {/* User Details */}
                                <YStack gap="$3">
                                    <XStack alignItems="center" gap="$3">
                                        <Text fontSize="$4" fontWeight="600" color="$gray11" width={80}>
                                            {t('auth.firstName', { defaultValue: 'Name' })}:
                                        </Text>
                                        <Text fontSize="$4" color="$gray12" flex={1}>
                                            {user.firstName} {user.lastName}
                                        </Text>
                                    </XStack>
                                    <XStack alignItems="center" gap="$3">
                                        <Text fontSize="$4" fontWeight="600" color="$gray11" width={80}>
                                            {t('auth.email', { defaultValue: 'Email' })}:
                                        </Text>
                                        <Text fontSize="$4" color="$gray12" flex={1}>
                                            {user.email}
                                        </Text>
                                    </XStack>
                                    <XStack alignItems="center" gap="$3">
                                        <Text fontSize="$4" fontWeight="600" color="$gray11" width={80}>
                                            {t('auth.phoneNumber', { defaultValue: 'Phone' })}:
                                        </Text>
                                        <Text fontSize="$4" color="$gray12" flex={1}>
                                            {user.phoneNumber}
                                        </Text>
                                    </XStack>
                                </YStack>
                            </YStack>
                        </Card>
                    </MotiView>

                    {/* Address Information Card */}
                    <MotiView
                        from={{ opacity: 0, translateY: 20 }}
                        animate={{ opacity: 1, translateY: 0 }}
                        transition={{ delay: 400, type: 'spring', damping: 15 }}
                    >
                        <Card
                            br="$0"
                            borderWidth={0}
                            width="100%"
                            mx="$0"
                            backgroundColor="white"
                            shadowColor="#000"
                            shadowOffset={{ width: 0, height: 4 }}
                            shadowOpacity={0.1}
                            shadowRadius={8}
                            elevation={5}
                        >
                            <YStack p="$5" gap="$4">
                                {/* Header */}
                                <XStack alignItems="center" gap="$3">
                                    <View
                                        style={{
                                            backgroundColor: '#10b981',
                                            borderRadius: 12,
                                            width: 48,
                                            height: 48,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                        }}
                                    >
                                        <Ionicons name="location" size={24} color="white" />
                                    </View>
                                    <YStack flex={1}>
                                        <H4 fontSize="$6" fontWeight="700" color="$gray12">
                                            {t('auth.address', { defaultValue: 'Address Information' })}
                                        </H4>
                                        <Text fontSize="$3" color="$gray9">
                                            {t('profile.currentAddress', { defaultValue: 'Your current address details' })}
                                        </Text>
                                    </YStack>
                                </XStack>

                                {/* Address Details */}
                                <YStack gap="$3">
                                    <XStack alignItems="center" gap="$3">
                                        <Text fontSize="$4" fontWeight="600" color="$gray11" width={80}>
                                            {t('auth.address', { defaultValue: 'Address' })}:
                                        </Text>
                                        <Text fontSize="$4" color="$gray12" flex={1}>
                                            {user.address}
                                        </Text>
                                    </XStack>
                                    <XStack alignItems="center" gap="$3">
                                        <Text fontSize="$4" fontWeight="600" color="$gray11" width={80}>
                                            {t('auth.city', { defaultValue: 'City' })}:
                                        </Text>
                                        <Text fontSize="$4" color="$gray12" flex={1}>
                                            {user.city}
                                        </Text>
                                    </XStack>
                                    <XStack alignItems="center" gap="$3">
                                        <Text fontSize="$4" fontWeight="600" color="$gray11" width={80}>
                                            {t('auth.country', { defaultValue: 'Country' })}:
                                        </Text>
                                        <Text fontSize="$4" color="$gray12" flex={1}>
                                            {user.country}
                                        </Text>
                                    </XStack>
                                </YStack>
                            </YStack>
                        </Card>
                    </MotiView>

                    {/* Security Settings Card */}
                    <MotiView
                        from={{ opacity: 0, translateY: 20 }}
                        animate={{ opacity: 1, translateY: 0 }}
                        transition={{ delay: 500, type: 'spring', damping: 15 }}
                    >
                        <Card
                            br="$0"
                            borderWidth={0}
                            width="100%"
                            mx="$0"
                            backgroundColor="white"
                            shadowColor="#000"
                            shadowOffset={{ width: 0, height: 4 }}
                            shadowOpacity={0.1}
                            shadowRadius={8}
                            elevation={5}
                        >
                            <YStack p="$5" gap="$4">
                                {/* Header */}
                                <XStack alignItems="center" justifyContent="space-between">
                                    <XStack alignItems="center" gap="$3">
                                        <View
                                            style={{
                                                backgroundColor: '#ef4444',
                                                borderRadius: 12,
                                                width: 48,
                                                height: 48,
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                            }}
                                        >
                                            <Ionicons name="shield-checkmark" size={24} color="white" />
                                        </View>
                                        <YStack>
                                            <H4 fontSize="$6" fontWeight="700" color="$gray12">
                                                {t('profile.security', { defaultValue: 'Security Settings' })}
                                            </H4>
                                            <Text fontSize="$3" color="$gray9">
                                                {t('profile.manageAccountSecurity', { defaultValue: 'Manage your account security' })}
                                            </Text>
                                        </YStack>
                                    </XStack>
                                    <Pressable onPress={() => setEditMode('password')}>
                                        <View
                                            style={{
                                                backgroundColor: '#f1f5f9',
                                                borderRadius: 8,
                                                padding: 8,
                                            }}
                                        >
                                            <Ionicons name="pencil" size={20} color="#ef4444" />
                                        </View>
                                    </Pressable>
                                </XStack>

                                {/* Security Options */}
                                <YStack gap="$3">
                                    <Pressable onPress={() => setEditMode('password')}>
                                        <XStack alignItems="center" justifyContent="space-between" p="$3" backgroundColor="$gray2" borderRadius="$3">
                                            <XStack alignItems="center" gap="$3">
                                                <Ionicons name="lock-closed" size={20} color="#ef4444" />
                                                <Text fontSize="$4" fontWeight="600" color="$gray12">
                                                    {t('auth.changePassword', { defaultValue: 'Change Password' })}
                                                </Text>
                                            </XStack>
                                            <Ionicons name="chevron-forward" size={20} color="$gray9" />
                                        </XStack>
                                    </Pressable>
                                </YStack>
                            </YStack>
                        </Card>
                    </MotiView>

                    {/* Language Settings Card */}
                    <MotiView
                        from={{ opacity: 0, translateY: 20 }}
                        animate={{ opacity: 1, translateY: 0 }}
                        transition={{ delay: 600, type: 'spring', damping: 15 }}
                    >
                        <Card
                            br="$0"
                            borderWidth={0}
                            width="100%"
                            mx="$0"
                            backgroundColor="white"
                            shadowColor="#000"
                            shadowOffset={{ width: 0, height: 4 }}
                            shadowOpacity={0.1}
                            shadowRadius={8}
                            elevation={5}
                        >
                            <YStack p="$5" gap="$4">
                                {/* Header */}
                                <XStack alignItems="center" gap="$3">
                                    <View
                                        style={{
                                            backgroundColor: '#8b5cf6',
                                            borderRadius: 12,
                                            width: 48,
                                            height: 48,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                        }}
                                    >
                                        <Ionicons name="language" size={24} color="white" />
                                    </View>
                                    <YStack flex={1}>
                                        <H4 fontSize="$6" fontWeight="700" color="$gray12">
                                            {t('profile.language', { defaultValue: 'Language Settings' })}
                                        </H4>
                                        <Text fontSize="$3" color="$gray9">
                                            {t('profile.currentLanguage', { defaultValue: 'Current language' })}: {currentLanguage === 'en' ? 'English' : 'العربية'}
                                        </Text>
                                    </YStack>
                                </XStack>

                                {/* Language Switcher */}
                                <YStack>
                                    <LanguageSwitcher variant="sheet" />
                                </YStack>
                            </YStack>
                        </Card>
                    </MotiView>

                    {/* Account Status Card */}
                    <MotiView
                        from={{ opacity: 0, translateY: 20 }}
                        animate={{ opacity: 1, translateY: 0 }}
                        transition={{ delay: 700, type: 'spring', damping: 15 }}
                    >
                        <Card
                            br="$0"
                            borderWidth={0}
                            width="100%"
                            mx="$0"
                            backgroundColor="white"
                            shadowColor="#000"
                            shadowOffset={{ width: 0, height: 4 }}
                            shadowOpacity={0.1}
                            shadowRadius={8}
                            elevation={5}
                        >
                            <YStack p="$5" gap="$4">
                                {/* Header */}
                                <XStack alignItems="center" gap="$3">
                                    <View
                                        style={{
                                            backgroundColor: user.isEmailVerified ? '#10b981' : '#f59e0b',
                                            borderRadius: 12,
                                            width: 48,
                                            height: 48,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                        }}
                                    >
                                        <Ionicons
                                            name={user.isEmailVerified ? "checkmark-circle" : "warning"}
                                            size={24}
                                            color="white"
                                        />
                                    </View>
                                    <YStack flex={1}>
                                        <H4 fontSize="$6" fontWeight="700" color="$gray12">
                                            {t('profile.accountStatus', { defaultValue: 'Account Status' })}
                                        </H4>
                                        <Text fontSize="$3" color={user.isEmailVerified ? "$green9" : "$orange9"}>
                                            {user.isEmailVerified ? t('auth.emailVerified', { defaultValue: 'Email Verified' }) : t('auth.emailNotVerified', { defaultValue: 'Email Not Verified' })}
                                        </Text>
                                    </YStack>
                                </XStack>
                            </YStack>
                        </Card>
                    </MotiView>

                    {/* Logout Card */}
                    <MotiView
                        from={{ opacity: 0, translateY: 20 }}
                        animate={{ opacity: 1, translateY: 0 }}
                        transition={{ delay: 800, type: 'spring', damping: 15 }}
                    >
                        <Card
                            br="$0"
                            borderWidth={0}
                            width="100%"
                            mx="$0"
                            backgroundColor="white"
                            shadowColor="#000"
                            shadowOffset={{ width: 0, height: 4 }}
                            shadowOpacity={0.1}
                            shadowRadius={8}
                            elevation={5}
                        >
                            <Pressable onPress={handleLogout}>
                                <YStack p="$5" gap="$4">
                                    {/* Header */}
                                    <XStack alignItems="center" justifyContent="space-between">
                                        <XStack alignItems="center" gap="$3">
                                            <View
                                                style={{
                                                    backgroundColor: '#dc2626',
                                                    borderRadius: 12,
                                                    width: 48,
                                                    height: 48,
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                }}
                                            >
                                                <Ionicons name="log-out" size={24} color="white" />
                                            </View>
                                            <YStack>
                                                <H4 fontSize="$6" fontWeight="700" color="$gray12">
                                                    {t('auth.logout', { defaultValue: 'Logout' })}
                                                </H4>
                                                <Text fontSize="$3" color="$gray9">
                                                    {t('profile.signOutAccount', { defaultValue: 'Sign out of your account' })}
                                                </Text>
                                            </YStack>
                                        </XStack>
                                        <View
                                            style={{
                                                backgroundColor: '#fef2f2',
                                                borderRadius: 8,
                                                padding: 8,
                                            }}
                                        >
                                            <Ionicons name="chevron-forward" size={20} color="#dc2626" />
                                        </View>
                                    </XStack>
                                </YStack>
                            </Pressable>
                        </Card>
                    </MotiView>
                </YStack>
            </ScrollView>

            {/* Profile Edit Modal */}
            <Sheet
                modal
                open={editMode === 'profile'}
                onOpenChange={(open: boolean) => !open && setEditMode(null)}
                snapPoints={[90]}
                dismissOnSnapToBottom
            >
                <Sheet.Overlay />
                <Sheet.Handle />
                <Sheet.Frame padding="$4" flex={1}>
                    <YStack flex={1} gap="$4">
                        <YStack gap="$2">
                            <H4>Edit Profile</H4>
                            <Separator />
                        </YStack>

                        <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
                            <YStack gap="$3" paddingBottom="$4">
                                <YStack gap="$2">
                                    <Label htmlFor="firstName">{t('auth.firstName', { defaultValue: 'First Name' })}</Label>
                                    <Input
                                        id="firstName"
                                        value={profileForm.firstName || ''}
                                        onChangeText={(text) => setProfileForm(prev => ({ ...prev, firstName: text }))}
                                        placeholder={t('auth.enterFirstName', { defaultValue: 'Enter first name' })}
                                    />
                                </YStack>

                                <YStack gap="$2">
                                    <Label htmlFor="lastName">{t('auth.lastName', { defaultValue: 'Last Name' })}</Label>
                                    <Input
                                        id="lastName"
                                        value={profileForm.lastName || ''}
                                        onChangeText={(text) => setProfileForm(prev => ({ ...prev, lastName: text }))}
                                        placeholder={t('auth.enterLastName', { defaultValue: 'Enter last name' })}
                                    />
                                </YStack>

                                <YStack gap="$2">
                                    <Label htmlFor="phoneNumber">{t('auth.phone', { defaultValue: 'Phone Number' })}</Label>
                                    <Input
                                        id="phoneNumber"
                                        value={profileForm.phoneNumber || ''}
                                        onChangeText={(text) => setProfileForm(prev => ({ ...prev, phoneNumber: text }))}
                                        placeholder={t('auth.enterPhone', { defaultValue: 'Enter phone number' })}
                                        keyboardType="phone-pad"
                                    />
                                </YStack>

                                <YStack gap="$2">
                                    <Label htmlFor="address">{t('auth.address', { defaultValue: 'Address' })}</Label>
                                    <Input
                                        id="address"
                                        value={profileForm.address || ''}
                                        onChangeText={(text) => setProfileForm(prev => ({ ...prev, address: text }))}
                                        placeholder={t('auth.enterAddress', { defaultValue: 'Enter address' })}
                                    />
                                </YStack>

                                <YStack gap="$2">
                                    <Label htmlFor="city">{t('auth.city', { defaultValue: 'City' })}</Label>
                                    <Input
                                        id="city"
                                        value={profileForm.city || ''}
                                        onChangeText={(text) => setProfileForm(prev => ({ ...prev, city: text }))}
                                        placeholder={t('auth.enterCity', { defaultValue: 'Enter city' })}
                                    />
                                </YStack>

                                <YStack gap="$2">
                                    <Label htmlFor="country">{t('auth.country', { defaultValue: 'Country' })}</Label>
                                    <Input
                                        id="country"
                                        value={profileForm.country || ''}
                                        onChangeText={(text) => setProfileForm(prev => ({ ...prev, country: text }))}
                                        placeholder={t('auth.enterCountry', { defaultValue: 'Enter country' })}
                                    />
                                </YStack>

                                <YStack gap="$2">
                                    <Label htmlFor="dateOfBirth">{t('auth.dateOfBirth', { defaultValue: 'Date of Birth' })}</Label>
                                    <Input
                                        id="dateOfBirth"
                                        value={profileForm.dateOfBirth || ''}
                                        onChangeText={(text) => setProfileForm(prev => ({ ...prev, dateOfBirth: text }))}
                                        placeholder="YYYY-MM-DD"
                                    />
                                </YStack>

                                <YStack gap="$2">
                                    <Label htmlFor="gender">{t('auth.gender', { defaultValue: 'Gender' })}</Label>
                                    <Input
                                        id="gender"
                                        value={profileForm.gender || ''}
                                        onChangeText={(text) => setProfileForm(prev => ({ ...prev, gender: text as 'male' | 'female' | 'other' }))}
                                        placeholder={`${t('auth.male', { defaultValue: 'male' })}, ${t('auth.female', { defaultValue: 'female' })}, ${t('auth.other', { defaultValue: 'other' })}`}
                                    />
                                </YStack>
                            </YStack>
                        </ScrollView>

                        <XStack gap="$3" paddingTop="$2">
                            <Button
                                flex={1}
                                variant="outlined"
                                onPress={() => {
                                    resetProfileForm();
                                    setEditMode(null);
                                }}
                                disabled={updating}
                            >
                                {t('common.cancel', { defaultValue: 'Cancel' })}
                            </Button>
                            <Button
                                flex={1}
                                backgroundColor="#67B329"
                                onPress={handleUpdateProfile}
                                disabled={updating}
                            >
                                {updating ? <ActivityIndicator size="small" color="#fff" /> : t('common.save', { defaultValue: 'Save Changes' })}
                            </Button>
                        </XStack>
                    </YStack>
                </Sheet.Frame>
            </Sheet>

            {/* Password Change Modal */}
            <Sheet
                modal
                open={editMode === 'password'}
                onOpenChange={(open: boolean) => !open && setEditMode(null)}
                snapPoints={[60]}
                dismissOnSnapToBottom
            >
                <Sheet.Overlay />
                <Sheet.Handle />
                <Sheet.Frame padding="$4" gap="$4">
                    <H4>{t('auth.changePassword', { defaultValue: 'Change Password' })}</H4>
                    <Separator />

                    <YStack gap="$3">
                        <YStack gap="$2">
                            <Label htmlFor="currentPassword">{t('profile.currentPassword', { defaultValue: 'Current Password' })}</Label>
                            <Input
                                id="currentPassword"
                                value={passwordForm.currentPassword}
                                onChangeText={(text) => setPasswordForm(prev => ({ ...prev, currentPassword: text }))}
                                placeholder={t('profile.enterCurrentPassword', { defaultValue: 'Enter current password' })}
                                secureTextEntry
                            />
                        </YStack>

                        <YStack gap="$2">
                            <Label htmlFor="newPassword">{t('auth.newPassword', { defaultValue: 'New Password' })}</Label>
                            <Input
                                id="newPassword"
                                value={passwordForm.newPassword}
                                onChangeText={(text) => setPasswordForm(prev => ({ ...prev, newPassword: text }))}
                                placeholder={t('auth.enterNewPassword', { defaultValue: 'Enter new password (min 6 characters)' })}
                                secureTextEntry
                            />
                        </YStack>
                    </YStack>

                    <XStack gap="$3" marginTop="$4">
                        <Button
                            flex={1}
                            variant="outlined"
                            onPress={() => {
                                setPasswordForm({ currentPassword: '', newPassword: '' });
                                setEditMode(null);
                            }}
                            disabled={updating}
                        >
                            {t('common.cancel', { defaultValue: 'Cancel' })}
                        </Button>
                        <Button
                            flex={1}
                            backgroundColor="#67B329"
                            onPress={handleChangePassword}
                            disabled={updating}
                        >
                            {updating ? <ActivityIndicator size="small" color="#fff" /> : t('auth.changePassword', { defaultValue: 'Change Password' })}
                        </Button>
                    </XStack>
                </Sheet.Frame>
            </Sheet>

        </YStack>
    );
};