import { useEffect, useState, useCallback } from 'react';
import { Platform, ScrollView, Dimensions, Linking, TouchableWithoutFeedback, RefreshControl, Alert } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { View, Text, Button, YStack, XStack, Card, H4, H5, H6, Spin<PERSON> } from 'tamagui';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { useSendPackageStore } from './useSendPackageStore';
import { useRequestPickupStore } from './useRequestPickupStore';
import { useTranslation } from 'react-i18next';
import { getPackageByTrackingNumber } from '../../../services/apiService';
import MapView, { Marker, Polyline } from 'react-native-maps';

interface TrackingUpdate {
  status: string;
  timestamp: string;
  message: string;
  location?: {
    lat: number;
    lng: number;
  };
}

interface PackageTrackingData {
  packageId?: string; // Backend uses packageId
  trackingNumber?: string; // Keep for backward compatibility
  status: string;
  pickupAddress: {
    street: string;
    coordinates: { lat: number; lng: number };
  };
  deliveryAddress?: {
    street: string;
    coordinates: { lat: number; lng: number };
  };
  // Flat fields from backend
  recipientName: string;
  recipientPhone: string;
  senderName: string;
  senderPhone: string;
  packageDetails: {
    description: string;
    type?: string;
    weight?: number;
  };
  cost: number;
  priority: string;
  driverName?: string;
  driverPhone?: string;
  estimatedDeliveryTime?: string;
  actualDeliveryTime?: string;
  trackingUpdates: TrackingUpdate[];
  createdAt: string;
}

export default function PackageTracking() {
  const { id, type } = useLocalSearchParams<{ id: string; type: 'sent' | 'pickup' }>();
  const router = useRouter();
  const { t } = useTranslation();

  const sendRequests = useSendPackageStore((s) => s.sendRequests);
  const pickupRequests = useRequestPickupStore((s) => s.pickupRequests);
  const localRequest = type === 'sent' ? sendRequests[Number(id)] : pickupRequests[Number(id)];

  const [packageData, setPackageData] = useState<PackageTrackingData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch package data from backend
  const fetchPackageData = useCallback(async (isRefresh: boolean = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      // Use tracking number from URL params or fallback to local data
      let trackingNumber = id;

      // If id is just a number (index), try to get tracking number from local data
      if (!id.startsWith('PKG-') && !id.startsWith('PU-')) {
        trackingNumber = localRequest?.id || id;
      }

      if (!trackingNumber) {
        throw new Error('No tracking number available');
      }

      const data = await getPackageByTrackingNumber(trackingNumber);
      setPackageData(data);
    } catch (error) {
      console.error('Error fetching package data:', error);
      setError(t('packages.failedToLoad', { defaultValue: 'Failed to load package details' }));

      // Fallback to local data if available
      if (localRequest) {
        setPackageData({
          packageId: localRequest.id || id,
          trackingNumber: localRequest.id || id,
          status: localRequest.status || 'pending',
          pickupAddress: {
            street: localRequest.pickup?.address || '',
            coordinates: { lat: localRequest.pickup?.lat || 0, lng: localRequest.pickup?.lng || 0 }
          },
          deliveryAddress: 'dropoff' in localRequest ? {
            street: localRequest.dropoff?.address || '',
            coordinates: { lat: localRequest.dropoff?.lat || 0, lng: localRequest.dropoff?.lng || 0 }
          } : undefined,
          // Use flat fields to match backend structure
          recipientName: localRequest.receiverName || 'Unknown',
          recipientPhone: localRequest.receiverPhone || '',
          senderName: 'You',
          senderPhone: '',
          packageDetails: {
            description: localRequest.packageType || 'Package',
            type: localRequest.packageType
          },
          cost: localRequest.cost || 0,
          priority: 'standard',
          driverName: localRequest.driverName,
          driverPhone: localRequest.driverPhone,
          trackingUpdates: [],
          createdAt: localRequest.createdAt || new Date().toISOString()
        });
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [id, localRequest, t]);

  useEffect(() => {
    fetchPackageData();
  }, [fetchPackageData]);

  const pickupLoc = packageData?.pickupAddress?.coordinates;
  const dropoffLoc = packageData?.deliveryAddress?.coordinates;

  const customerLocation = {
    latitude: pickupLoc?.lat || 32.2211,
    longitude: pickupLoc?.lng || 35.2544,
  };

  const [driverLocation, setDriverLocation] = useState({
    latitude: customerLocation.latitude - 0.01,
    longitude: customerLocation.longitude - 0.01,
  });

  // Enhanced driver location simulation
  useEffect(() => {
    if (!packageData || packageData.status === 'delivered') return;

    const interval = setInterval(() => {
      setDriverLocation((prev) => {
        const targetLat = packageData.deliveryAddress?.coordinates.lat || customerLocation.latitude;
        const targetLng = packageData.deliveryAddress?.coordinates.lng || customerLocation.longitude;

        const latDiff = targetLat - prev.latitude;
        const lonDiff = targetLng - prev.longitude;
        const step = 0.0003;

        const newLat = prev.latitude + Math.sign(latDiff) * Math.min(Math.abs(latDiff), step);
        const newLon = prev.longitude + Math.sign(lonDiff) * Math.min(Math.abs(lonDiff), step);

        if (Math.abs(latDiff) < 0.0001 && Math.abs(lonDiff) < 0.0001) {
          clearInterval(interval);
          return prev;
        }

        return { latitude: newLat, longitude: newLon };
      });
    }, 2000);
    return () => clearInterval(interval);
  }, [packageData, customerLocation]);

  const [eta, setEta] = useState(30 * 60);
  useEffect(() => {
    if (eta <= 0 || !packageData || packageData.status === 'delivered') return;
    const timer = setInterval(() => setEta((e) => Math.max(0, e - 1)), 1000);
    return () => clearInterval(timer);
  }, [eta, packageData]);

  const formatTime = (s: number) => {
    const hours = Math.floor(s / 3600);
    const minutes = Math.floor((s % 3600) / 60);
    const seconds = s % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m ${seconds}s`;
  };

  const { height } = Dimensions.get('window');
  const [fullscreen, setFullscreen] = useState(false);

  // Enhanced status mapping
  const getStatusSteps = () => {
    const allStatuses = [
      { key: 'pending', label: t('packages.statuses.pending', { defaultValue: 'Pending' }), icon: 'time' },
      { key: 'confirmed', label: t('packages.statuses.confirmed', { defaultValue: 'Confirmed' }), icon: 'checkmark-circle' },
      { key: 'picked_up', label: t('packages.statuses.picked_up', { defaultValue: 'Picked Up' }), icon: 'hand-left' },
      { key: 'in_transit', label: t('packages.statuses.in_transit', { defaultValue: 'In Transit' }), icon: 'car' },
      { key: 'out_for_delivery', label: t('packages.statuses.out_for_delivery', { defaultValue: 'Out for Delivery' }), icon: 'bicycle' },
      { key: 'delivered', label: t('packages.statuses.delivered', { defaultValue: 'Delivered' }), icon: 'checkmark-done' }
    ];

    const currentStatusIndex = allStatuses.findIndex(s => s.key === packageData?.status);
    return allStatuses.map((status, index) => ({
      ...status,
      completed: index <= currentStatusIndex,
      current: index === currentStatusIndex
    }));
  };

  const onRefresh = useCallback(() => {
    fetchPackageData(true);
  }, [fetchPackageData]);

  if (loading && !packageData) {
    return (
      <View flex={1} ai="center" jc="center" bg="$background">
        <Spinner size="large" color="$primary" />
        <Text mt="$3" color="$gray9" fontSize="$5">
          {t('packages.loadingTracking', { defaultValue: 'Loading tracking details...' })}
        </Text>
      </View>
    );
  }

  if (error && !packageData) {
    return (
      <View flex={1} ai="center" jc="center" bg="$background" p="$4">
        <Ionicons name="alert-circle" size={64} color="#ef4444" />
        <Text mt="$3" color="$red10" fontSize="$6" fontWeight="600" textAlign="center">
          {error}
        </Text>
        <Button
          mt="$4"
          size="$4"
          bg="$primary"
          color="white"
          br="$6"
          onPress={() => fetchPackageData()}
        >
          {t('common.retry', { defaultValue: 'Retry' })}
        </Button>
        <Button
          mt="$2"
          size="$4"
          variant="outlined"
          br="$6"
          onPress={() => router.back()}
        >
          {t('common.goBack', { defaultValue: 'Go Back' })}
        </Button>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, width: '100%', marginHorizontal: 0, paddingHorizontal: 0 }}>
      {fullscreen && (
        <View style={{ position: 'absolute', top: 0, bottom: 0, left: 0, right: 0, zIndex: 999 }}>
          <MapView
            style={{ flex: 1 }}
            region={{
              latitude: customerLocation.latitude,
              longitude: customerLocation.longitude,
              latitudeDelta: 0.01,
              longitudeDelta: 0.01,
            }}
          >
            <Marker
              coordinate={customerLocation}
              pinColor="green"
              title="Pickup Location"
            />
            <Marker
              coordinate={driverLocation}
              pinColor="purple"
              title="Driver Location"
            />
            <Polyline
              coordinates={[driverLocation, customerLocation]}
              strokeColor="#007AFF"
              strokeWidth={3}
            />
          </MapView>

          <Button
            onPress={() => setFullscreen(false)}
            bg="$primary"
            color="white"
            style={{
              position: 'absolute',
              top: 40,
              right: 20,
              zIndex: 1000,
              padding: 10,
            }}
            br={100}
            hoverStyle={{ bg: '$third' }}
            pressStyle={{ bg: '$third' }}
          >
            <Ionicons name="close" size={20} color="#fff" />
          </Button>
        </View>
      )}

      <ScrollView
        contentContainerStyle={{ flexGrow: 1, paddingHorizontal: 0, width: '100%' }}
        style={{ flex: 1, marginHorizontal: 0, width: '100%' }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {!fullscreen && (
          <>
            {/* Enhanced Header */}
            <View
              style={{
                paddingTop: 50,
                paddingBottom: 30,
                paddingHorizontal: 0,
                borderBottomLeftRadius: 0,
                borderBottomRightRadius: 0,
                backgroundImage: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                backgroundColor: '#667eea',
                width: '100%',
                marginHorizontal: 0,
              }}
            >
              <MotiView from={{ opacity: 0, translateY: -20 }} animate={{ opacity: 1, translateY: 0 }}>
                <YStack ai="center" gap="$2" px="$4">
                  <Text fontSize="$9" fontWeight="800" color="white" textAlign="center">
                    📦 {t('packages.trackingDetails', { defaultValue: 'Package Tracking' })}
                  </Text>
                  {packageData && (
                    <Text fontSize="$5" color="white" opacity={0.9} textAlign="center">
                      #{packageData.packageId || packageData.trackingNumber}
                    </Text>
                  )}
                </YStack>
              </MotiView>
            </View>

            <YStack p="$0" gap="$2" width="100%" mx="$0">
              {/* Enhanced Status Stepper */}
              <Card bg="white" p="$4" br="$0" elevate mx="$0" width="100%">
                <H5 mb="$4" textAlign="center" color="$gray11">
                  {t('packages.deliveryProgress', { defaultValue: 'Delivery Progress' })}
                </H5>

                <YStack gap="$3">
                  {getStatusSteps().map((status, index) => (
                    <MotiView
                      key={status.key}
                      from={{ opacity: 0, translateX: -20 }}
                      animate={{ opacity: 1, translateX: 0 }}
                      transition={{ delay: index * 100 }}
                    >
                      <XStack ai="center" gap="$3" p="$3" br="$6"
                        bg={status.completed ? '$green1' : status.current ? '$blue1' : '$gray1'}
                        bc={status.completed ? '$green6' : status.current ? '$blue6' : '$gray6'}
                        bw={1}
                      >
                        <View
                          bg={status.completed ? '$green9' : status.current ? '$blue9' : '$gray6'}
                          p="$2"
                          br="$6"
                        >
                          <Ionicons
                            name={status.icon as keyof typeof Ionicons.glyphMap}
                            size={20}
                            color={status.completed || status.current ? 'white' : '#9ca3af'}
                          />
                        </View>

                        <YStack flex={1}>
                          <Text
                            fontSize="$4"
                            fontWeight="600"
                            color={status.completed ? '$green10' : status.current ? '$blue10' : '$gray9'}
                          >
                            {status.label}
                          </Text>
                          {status.current && packageData?.trackingUpdates?.length > 0 && (
                            <Text fontSize="$3" color="$gray8" mt="$1">
                              {new Date(packageData.trackingUpdates[packageData.trackingUpdates.length - 1].timestamp).toLocaleString()}
                            </Text>
                          )}
                        </YStack>

                        {status.completed && (
                          <Ionicons name="checkmark-circle" size={24} color="#10b981" />
                        )}
                      </XStack>
                    </MotiView>
                  ))}
                </YStack>
              </Card>

              {/* ETA and Cost Information */}
              <XStack gap="$0" mx="$0" px="$0" width="100%">
                <Card flex={1} bg="$blue1" p="$4" br="$0" bc="$blue6" bw={1}>
                  <YStack ai="center" gap="$2">
                    <Ionicons name="time" size={24} color="#3b82f6" />
                    <Text fontSize="$3" color="$blue10" textAlign="center">
                      {t('packages.estimatedArrival', { defaultValue: 'ETA' })}
                    </Text>
                    <Text fontSize="$6" fontWeight="bold" color="$blue11" textAlign="center">
                      {packageData?.status === 'delivered'
                        ? t('packages.delivered', { defaultValue: 'Delivered' })
                        : formatTime(eta)
                      }
                    </Text>
                  </YStack>
                </Card>

                {packageData?.cost && (
                  <Card flex={1} bg="$green1" p="$4" br="$0" bc="$green6" bw={1}>
                    <YStack ai="center" gap="$2">
                      <Ionicons name="cash" size={24} color="#10b981" />
                      <Text fontSize="$3" color="$green10" textAlign="center">
                        {t('packages.totalCost', { defaultValue: 'Total Cost' })}
                      </Text>
                      <Text fontSize="$6" fontWeight="bold" color="$green11" textAlign="center">
                        ${packageData.cost.toFixed(2)}
                      </Text>
                    </YStack>
                  </Card>
                )}
              </XStack>

              {/* Enhanced Driver Information */}
              {packageData?.driverName && (
                <Card bg="white" p="$4" br="$0" elevate mx="$0" width="100%">
                  <H6 mb="$3" color="$gray11">
                    {t('packages.driverInfo', { defaultValue: 'Driver Information' })}
                  </H6>

                  <XStack ai="center" jc="space-between">
                    <XStack ai="center" gap="$3" flex={1}>
                      <View
                        bg="$primary"
                        p="$3"
                        br="$8"
                        style={{
                          shadowColor: '#667eea',
                          shadowOpacity: 0.3,
                          shadowRadius: 8,
                          elevation: 4,
                        }}
                      >
                        <Ionicons name="person" size={24} color="white" />
                      </View>

                      <YStack flex={1}>
                        <Text fontSize="$5" fontWeight="600" color="$gray11">
                          {packageData.driverName}
                        </Text>
                        {packageData.driverPhone && (
                          <Text fontSize="$3" color="$gray9" mt="$1">
                            {packageData.driverPhone}
                          </Text>
                        )}
                      </YStack>
                    </XStack>

                    {packageData.driverPhone && (
                      <XStack gap="$2">
                        <Button
                          size="$3"
                          br="$8"
                          bg="$green9"
                          onPress={() => Linking.openURL(`tel:${packageData.driverPhone}`)}
                          style={{
                            shadowColor: '#10b981',
                            shadowOpacity: 0.3,
                            shadowRadius: 6,
                            elevation: 3,
                          }}
                        >
                          <Ionicons name="call" size={18} color="white" />
                        </Button>

                        <Button
                          size="$3"
                          br="$8"
                          bg="$blue9"
                          onPress={() => Linking.openURL(`sms:${packageData.driverPhone}`)}
                          style={{
                            shadowColor: '#3b82f6',
                            shadowOpacity: 0.3,
                            shadowRadius: 6,
                            elevation: 3,
                          }}
                        >
                          <Ionicons name="chatbubble" size={18} color="white" />
                        </Button>
                      </XStack>
                    )}
                  </XStack>
                </Card>
              )}

              {/* Enhanced Map Section */}
              <Card bg="white" p="$0" br="$0" elevate overflow="hidden" mx="$0" width="100%">
                <YStack>
                  <XStack ai="center" jc="space-between" p="$4" bg="$gray1">
                    <H6 color="$gray11">
                      {t('packages.liveTracking', { defaultValue: 'Live Tracking' })}
                    </H6>
                    <Button
                      size="$3"
                      br="$6"
                      bg="$primary"
                      onPress={() => setFullscreen(true)}
                    >
                      <XStack ai="center" gap="$2">
                        <Ionicons name="expand" size={16} color="white" />
                        <Text color="white" fontSize="$3">
                          {t('packages.fullscreen', { defaultValue: 'Fullscreen' })}
                        </Text>
                      </XStack>
                    </Button>
                  </XStack>

                  <TouchableWithoutFeedback onPress={() => setFullscreen(true)}>
                    <View style={{ height: height * 0.4, position: 'relative' }}>
                      <MapView
                        style={{ flex: 1 }}
                        initialRegion={{
                          latitude: customerLocation.latitude,
                          longitude: customerLocation.longitude,
                          latitudeDelta: 0.02,
                          longitudeDelta: 0.02,
                        }}
                        showsUserLocation={false}
                        showsMyLocationButton={false}
                        showsCompass={true}
                        showsScale={true}
                      >
                        {/* Pickup Location */}
                        {pickupLoc && (
                          <Marker
                            coordinate={{
                              latitude: pickupLoc.lat,
                              longitude: pickupLoc.lng
                            }}
                            title={t('packages.pickupLocation', { defaultValue: 'Pickup Location' })}
                            description={packageData?.pickupAddress.street}
                          >
                            <View style={{
                              width: 40,
                              height: 40,
                              borderRadius: 20,
                              backgroundColor: '#10b981',
                              alignItems: 'center',
                              justifyContent: 'center',
                              borderWidth: 3,
                              borderColor: 'white',
                              shadowColor: '#000',
                              shadowOpacity: 0.3,
                              shadowRadius: 4,
                              elevation: 5,
                            }}>
                              <Ionicons name="location" size={20} color="white" />
                            </View>
                          </Marker>
                        )}

                        {/* Delivery Location */}
                        {dropoffLoc && (
                          <Marker
                            coordinate={{
                              latitude: dropoffLoc.lat,
                              longitude: dropoffLoc.lng
                            }}
                            title={t('packages.deliveryLocation', { defaultValue: 'Delivery Location' })}
                            description={packageData?.deliveryAddress?.street}
                          >
                            <View style={{
                              width: 40,
                              height: 40,
                              borderRadius: 20,
                              backgroundColor: '#3b82f6',
                              alignItems: 'center',
                              justifyContent: 'center',
                              borderWidth: 3,
                              borderColor: 'white',
                              shadowColor: '#000',
                              shadowOpacity: 0.3,
                              shadowRadius: 4,
                              elevation: 5,
                            }}>
                              <Ionicons name="flag" size={20} color="white" />
                            </View>
                          </Marker>
                        )}

                        {/* Driver Location */}
                        {packageData?.status !== 'delivered' && (
                          <Marker
                            coordinate={{
                              latitude: driverLocation.latitude,
                              longitude: driverLocation.longitude
                            }}
                            title={t('packages.driverLocation', { defaultValue: 'Driver Location' })}
                            description={packageData?.driverName || t('packages.driver', { defaultValue: 'Driver' })}
                          >
                            <View style={{
                              width: 40,
                              height: 40,
                              borderRadius: 20,
                              backgroundColor: '#8b5cf6',
                              alignItems: 'center',
                              justifyContent: 'center',
                              borderWidth: 3,
                              borderColor: 'white',
                              shadowColor: '#000',
                              shadowOpacity: 0.3,
                              shadowRadius: 4,
                              elevation: 5,
                            }}>
                              <Ionicons name="car" size={20} color="white" />
                            </View>
                          </Marker>
                        )}

                        {/* Route Polyline */}
                        {pickupLoc && dropoffLoc && (
                          <Polyline
                            coordinates={[
                              { latitude: pickupLoc.lat, longitude: pickupLoc.lng },
                              { latitude: driverLocation.latitude, longitude: driverLocation.longitude },
                              { latitude: dropoffLoc.lat, longitude: dropoffLoc.lng }
                            ]}
                            strokeColor="#667eea"
                            strokeWidth={4}
                            strokePattern={[10, 5]}
                          />
                        )}
                      </MapView>
                    </View>
                  </TouchableWithoutFeedback>
                </YStack>
              </Card>

              {/* Enhanced Action Buttons */}
              <YStack gap="$3" mt="$4" px="$3">
                {packageData?.status !== 'delivered' && (
                  <Button
                    size="$5"
                    bg="$orange9"
                    color="white"
                    br="$8"
                    fontWeight="600"
                    onPress={() => {
                      Alert.alert(
                        t('packages.reportIssue', { defaultValue: 'Report Issue' }),
                        t('packages.reportIssueDesc', { defaultValue: 'Would you like to report an issue with this delivery?' }),
                        [
                          { text: t('common.cancel', { defaultValue: 'Cancel' }), style: 'cancel' },
                          {
                            text: t('packages.reportIssue', { defaultValue: 'Report Issue' }),
                            onPress: () => {
                              // Navigate to support or open contact
                              Linking.openURL('mailto:<EMAIL>?subject=Package Issue&body=Package ID: ' + (packageData.packageId || packageData.trackingNumber));
                            }
                          }
                        ]
                      );
                    }}
                    style={{
                      shadowColor: '#f59e0b',
                      shadowOpacity: 0.3,
                      shadowRadius: 8,
                      elevation: 4,
                    }}
                  >
                    <XStack ai="center" gap="$3">
                      <Ionicons name="warning" size={20} color="white" />
                      <Text color="white" fontSize="$5" fontWeight="600">
                        {t('packages.reportIssue', { defaultValue: 'Report Issue' })}
                      </Text>
                    </XStack>
                  </Button>
                )}

                <XStack gap="$3">
                  <Button
                    flex={1}
                    size="$5"
                    variant="outlined"
                    br="$8"
                    bc="$primary"
                    color="$primary"
                    fontWeight="600"
                    onPress={() => router.back()}
                  >
                    <XStack ai="center" gap="$2">
                      <Ionicons name="arrow-back" size={20} color="#667eea" />
                      <Text color="$primary" fontSize="$5" fontWeight="600">
                        {t('common.goBack', { defaultValue: 'Go Back' })}
                      </Text>
                    </XStack>
                  </Button>

                  <Button
                    flex={1}
                    size="$5"
                    bg="$primary"
                    color="white"
                    br="$8"
                    fontWeight="600"
                    onPress={() => fetchPackageData(true)}
                    style={{
                      shadowColor: '#667eea',
                      shadowOpacity: 0.3,
                      shadowRadius: 8,
                      elevation: 4,
                    }}
                  >
                    <XStack ai="center" gap="$2">
                      <Ionicons name="refresh" size={20} color="white" />
                      <Text color="white" fontSize="$5" fontWeight="600">
                        {t('common.refresh', { defaultValue: 'Refresh' })}
                      </Text>
                    </XStack>
                  </Button>
                </XStack>
              </YStack>
            </YStack>
            {/* Bottom padding for safe area */}
            <View style={{ height: 100 }} />
          </>
        )}
      </ScrollView>
    </View>
  );
}
